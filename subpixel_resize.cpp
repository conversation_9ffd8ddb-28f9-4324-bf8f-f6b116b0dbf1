#include <opencv2/opencv.hpp>
#include <iostream>

cv::Mat extractAndResizeSubpixelRegion(const cv::Mat& src, 
                                       cv::Point2f center, 
                                       cv::Size2f regionSize, 
                                       cv::Size targetSize,
                                       int interpolation = cv::INTER_LINEAR) {
    cv::Mat extracted, result;
    
    // 使用getRectSubPix提取子像素区域
    cv::getRectSubPix(src, regionSize, center, extracted);
    
    // 将提取的区域resize到目标分辨率
    cv::resize(extracted, result, targetSize, 0, 0, interpolation);
    
    return result;
}

int main() {
    // 加载图像
    cv::Mat img = cv::imread("input.jpg");
    
    // 定义子像素区域 - 中心点和尺寸都可以是浮点数
    cv::Point2f center(100.5f, 200.3f);  // 子像素中心点
    cv::Size2f regionSize(50.7f, 80.2f); // 子像素区域尺寸
    cv::Size targetSize(200, 300);       // 目标分辨率
    
    // 提取并缩放
    cv::Mat result = extractAndResizeSubpixelRegion(img, center, regionSize, 
                                                   targetSize, cv::INTER_LINEAR);
    
    cv::imwrite("output.jpg", result);
    return 0;
}