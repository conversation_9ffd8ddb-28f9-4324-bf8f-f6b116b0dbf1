import cv2 as cv
import numpy as np
import os
import argparse

def load_remap_maps_npy(xmap_path, ymap_path):
    """加载npy格式的xmap和ymap文件"""
    xmap = np.load(xmap_path)
    ymap = np.load(ymap_path)
    return xmap, ymap

def resize_maps(xmap, ymap, target_size):
    """将map调整到目标尺寸"""
    target_height, target_width = target_size
    
    # 使用双线性插值调整map尺寸
    xmap_resized = cv.resize(xmap, (target_width, target_height), interpolation=cv.INTER_LINEAR)
    ymap_resized = cv.resize(ymap, (target_width, target_height), interpolation=cv.INTER_LINEAR)
    
    # 调整坐标值以适应新的尺寸
    height_scale = target_height / xmap.shape[0]
    width_scale = target_width / xmap.shape[1]
    
    xmap_resized = xmap_resized * width_scale
    ymap_resized = ymap_resized * height_scale
    
    return xmap_resized, ymap_resized

def crop_maps_16_3_ratio(xmap, ymap):
    """按16:3比例从底部截取map"""
    height, width = xmap.shape
    
    # 计算16:3比例的高度
    target_height = int(width * 3 / 16)
    
    if target_height > height:
        print(f"警告: 目标高度 {target_height} 超过原始高度 {height}")
        target_height = height
    
    # 从底部截取
    start_y = height - target_height
    
    cropped_xmap = xmap[:target_height, :]
    cropped_ymap = ymap[:target_height, :]
    
    return cropped_xmap, cropped_ymap


def compose_remap_maps(xmap1, ymap1, xmap2, ymap2):
    """
    合并两个remap映射，等效于先应用map1再应用map2
    
    Args:
        xmap1, ymap1: 第一次remap的映射
        xmap2, ymap2: 第二次remap的映射
    
    Returns:
        composed_xmap, composed_ymap: 合并后的映射
    """
    height1, width1 = xmap1.shape
    height, width = xmap2.shape
    composed_xmap = np.zeros_like(xmap2)
    composed_ymap = np.zeros_like(ymap2)
    
    for y in range(height):
        for x in range(width):
            # 第二个映射的坐标
            intermediate_x = xmap2[y, x]
            intermediate_y = ymap2[y, x]
            
            # 检查中间坐标是否在第一个映射的范围内
            if (0 <= intermediate_x < width1 and 0 <= intermediate_y < height1):
                # 使用双线性插值从第一个映射中获取最终坐标
                ix = int(intermediate_x)
                iy = int(intermediate_y)
                fx = intermediate_x - ix
                fy = intermediate_y - iy
                
                # 边界检查
                ix1 = min(ix + 1, width - 1)
                iy1 = min(iy + 1, height - 1)
                
                # 双线性插值
                final_x = (1-fx)*(1-fy)*xmap1[iy, ix] + fx*(1-fy)*xmap1[iy, ix1] + \
                         (1-fx)*fy*xmap1[iy1, ix] + fx*fy*xmap1[iy1, ix1]
                final_y = (1-fx)*(1-fy)*ymap1[iy, ix] + fx*(1-fy)*ymap1[iy, ix1] + \
                         (1-fx)*fy*ymap1[iy1, ix] + fx*fy*ymap1[iy1, ix1]
                
                composed_xmap[y, x] = final_x
                composed_ymap[y, x] = final_y
            else:
                # 超出范围的像素设为无效值
                composed_xmap[y, x] = -1
                composed_ymap[y, x] = -1
    
    return composed_xmap, composed_ymap


def load_and_compose_maps_npy(xmap1_path, ymap1_path, xmap2_path, ymap2_path):
    """加载并合并两组npy格式的remap映射"""
    # 加载第一组映射
    xmap1, ymap1 = load_remap_maps_npy(xmap1_path, ymap1_path)
    print(f"Map1 尺寸: {xmap1.shape}")
    
    # 加载第二组映射
    xmap2, ymap2 = load_remap_maps_npy(xmap2_path, ymap2_path)
    print(f"Map2 尺寸: {xmap2.shape}")
    
    # 如果尺寸不一致，将map1调整到map2的尺寸
    # if xmap1.shape != xmap2.shape:
    #     print(f"尺寸不一致，将Map1从 {xmap1.shape} 调整到 {xmap2.shape}")
    #     xmap1, ymap1 = resize_maps(xmap1, ymap1, xmap2.shape)
    
    # 合并映射
    composed_xmap, composed_ymap = compose_remap_maps(xmap1, ymap1, xmap2, ymap2)
    print(f"合并后Map尺寸: {composed_xmap.shape}")
    
    # # 按16:3比例截取
    # cropped_xmap, cropped_ymap = crop_maps_16_3_ratio(composed_xmap, composed_ymap)
    # print(f"截取后Map尺寸: {cropped_xmap.shape}")
    
    return composed_xmap, composed_ymap

if __name__ == "__main__":
    # xmap = np.fromfile(r"D:\xmap.bin", dtype=np.float32)
    # ymap = np.fromfile(r"D:\ymap.bin", dtype=np.float32)
    # xmap = xmap.reshape(2160, 3840)
    # ymap = ymap.reshape(2160, 3840)

    # np.save(r"D:\Dataset\image\mapx0.npy", xmap)
    # np.save(r"D:\Dataset\image\mapy0.npy", ymap)
    # quit()
    # 示例使用
    bcalculate = True
    if bcalculate:
        xmap1_path = 'D:/Dataset/image/mapx0.npy'
        ymap1_path = 'D:/Dataset/image/mapy0.npy'
        xmap2_path = 'D:/Dataset/image/mapx.npy'
        ymap2_path = 'D:/Dataset/image/mapy.npy'
        
        # 加载并合并maps
        final_xmap, final_ymap = load_and_compose_maps_npy(
            xmap1_path, ymap1_path, xmap2_path, ymap2_path
        )

        np.save("D:/Dataset/image/final_xmap_temp.npy", final_xmap)
        np.save("D:/Dataset/image/final_ymap_temp.npy", final_ymap)

        xmap1_path = 'D:/Dataset/image/final_xmap_temp.npy'
        ymap1_path = 'D:/Dataset/image/final_ymap_temp.npy'
        xmap2_path = 'D:/Dataset/image/mapx2.npy'
        ymap2_path = 'D:/Dataset/image/mapy2.npy'
        
        # 加载并合并maps
        final_xmap, final_ymap = load_and_compose_maps_npy(
            xmap1_path, ymap1_path, xmap2_path, ymap2_path
        )
        
        # 保存最终的map
        np.save('D:/Dataset/image/final_xmap.npy', final_xmap)
        np.save('D:/Dataset/image/final_ymap.npy', final_ymap)

        # final_xmap = np.load('D:/Dataset/image/final_xmap.npy')
        # final_ymap = np.load('D:/Dataset/image/final_ymap.npy')
        # print(final_xmap.shape, final_ymap.shape)

        h, w = final_xmap.shape
        #newh = w * 3 // 16
        newh = w * 9 * 2 // (16 * 7)
        final_xmap = final_xmap[:newh, :]
        final_ymap = final_ymap[:newh, :]
        np.save('D:/Dataset/image/final_xmap_cropped_2.npy', final_xmap)
        np.save('D:/Dataset/image/final_ymap_cropped_2.npy', final_ymap)
    else:
        final_xmap = np.load('D:/Dataset/image/final_xmap_cropped.npy')
        final_ymap = np.load('D:/Dataset/image/final_ymap_cropped.npy')
        final_xmap = cv.resize(final_xmap, (1920, 360), interpolation=cv.INTER_LINEAR)
        final_ymap = cv.resize(final_ymap, (1920, 360), interpolation=cv.INTER_LINEAR)
    
    total_final_xmap = np.ones((1080, 1920), dtype=np.float32) * -1
    total_final_ymap = np.ones((1080, 1920), dtype=np.float32) * -1
    total_final_xmap[-final_xmap.shape[0]:, :] = final_xmap
    total_final_ymap[-final_ymap.shape[0]:, :] = final_ymap
    final_xmap = total_final_xmap
    final_ymap = total_final_ymap
    final_xmap.tofile("D:/Dataset/image/final_xmap_2.bin")
    final_ymap.tofile("D:/Dataset/image/final_ymap_2.bin")

    # 测试应用到图像
    img = cv.imread("D:/result.jpg")
    if img is not None:
        result = cv.remap(img, final_xmap, final_ymap, cv.INTER_LINEAR, cv.BORDER_CONSTANT)
        cv.imwrite('D:/Dataset/image/final_output.jpg', result)
        print("图像处理完成")