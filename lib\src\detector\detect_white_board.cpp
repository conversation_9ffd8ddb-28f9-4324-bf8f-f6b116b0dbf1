#include "detect_white_board.h"

namespace czcv_camera
{
    Status Detect_White_Board::init(std::vector<std::string> modelConfig)
    {
        int ret = rknn_init(&ctx, (void*)modelConfig[0].c_str(), 0, 0, NULL);

        ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
		if (ret < 0)
		{
			LOGE("rknn_init error ret=%d\n", ret);
			return -1;
		}

		input_attrs.resize(io_num.n_input);
		memset(input_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_input);
		for (int i = 0; i < io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret < 0)
			{
				LOGE("rknn_init error ret=%d\n", ret);
				return -1;
			}
		}

		output_attrs.resize(io_num.n_output);
		memset(output_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_output);
		for (int i = 0; i < io_num.n_output; i++)
		{
			output_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
		}

        if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
		{
			//LOGE("model is NCHW input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputChannel = input_attrs[0].dims[1];
			_inputHeight = input_attrs[0].dims[2];
			_inputWidth = input_attrs[0].dims[3];
		}
		else
		{
			//LOGE("model is NHWC input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputHeight = input_attrs[0].dims[1];
			_inputWidth = input_attrs[0].dims[2];
			_inputChannel = input_attrs[0].dims[3];
		}

        input_attrs[0].type = RKNN_TENSOR_UINT8;
		// default fmt is NHWC, npu only support NHWC in zero copy mode
		input_attrs[0].fmt = RKNN_TENSOR_NHWC;

        for (int i = 0; i < 6; i++)
        {
            // default output type is depend on model, this require float32 to compute top5
            output_attrs[i].type = RKNN_TENSOR_FLOAT32;
        }

        GenerateMeshgrid();
        return CZCV_OK;
    }

    void Detect_White_Board::GenerateMeshgrid() {
		meshgrid.clear();
		for (int index = 0; index < head_num; ++index) {
			for (int i = 0; i < map_size[index][0]; ++i) {
				for (int j = 0; j < map_size[index][1]; ++j) {
					meshgrid.push_back(j + 0.5f);
					meshgrid.push_back(i + 0.5f);
				}
			}
		}
	}

	// Sigmoid函数
	inline float sigmoid(float x) {
		return 1.0f / (1.0f + std::exp(-x));
	}

	// TopK筛选
	std::vector<DetectBox> Detect_White_Board::TopK(const std::vector<DetectBox>& detectResult) {
		if (detectResult.size() <= topK) {
			return detectResult;
		}
		
		// 按置信度降序排序
		std::vector<DetectBox> sorted = detectResult;
		std::sort(sorted.begin(), sorted.end(), 
			[](const DetectBox& a, const DetectBox& b) {
				return a.score > b.score;
			});
		
		// 取前topK个
		return std::vector<DetectBox>(sorted.begin(), sorted.begin() + topK);
	}

	// 计算两个边界框的IoU（交并比）
	float Detect_White_Board::calculateIoU(const DetectBox& box1, const DetectBox& box2) {
		// 计算交集区域的坐标
		float x1 = std::max(box1.xmin, box2.xmin);
		float y1 = std::max(box1.ymin, box2.ymin);
		float x2 = std::min(box1.xmax, box2.xmax);
		float y2 = std::min(box1.ymax, box2.ymax);
		
		// 如果没有交集，返回0
		if (x2 <= x1 || y2 <= y1) {
			return 0.0f;
		}
		
		// 计算交集面积
		float intersection = (x2 - x1) * (y2 - y1);
		
		// 计算两个框的面积
		float area1 = (box1.xmax - box1.xmin) * (box1.ymax - box1.ymin);
		float area2 = (box2.xmax - box2.xmin) * (box2.ymax - box2.ymin);
		
		// 计算并集面积
		float unionArea = area1 + area2 - intersection;
		
		// 避免除零
		if (unionArea <= 0) {
			return 0.0f;
		}
		
		return intersection / unionArea;
	}

	// NMS非极大值抑制算法
	std::vector<DetectBox> Detect_White_Board::applyNMS(const std::vector<DetectBox>& boxes, float nms_threshold) {
		if (boxes.empty()) {
			return {};
		}
		
		// 按置信度降序排序
		std::vector<DetectBox> sorted_boxes = boxes;
		std::sort(sorted_boxes.begin(), sorted_boxes.end(), 
			[](const DetectBox& a, const DetectBox& b) {
				return a.score > b.score;
			});
		
		std::vector<bool> suppressed(sorted_boxes.size(), false);
		std::vector<DetectBox> result;
		
		for (size_t i = 0; i < sorted_boxes.size(); ++i) {
			if (suppressed[i]) {
				continue;
			}
			
			// 保留当前框
			result.push_back(sorted_boxes[i]);
			
			// 抑制与当前框IoU大于阈值的其他框
			for (size_t j = i + 1; j < sorted_boxes.size(); ++j) {
				if (suppressed[j]) {
					continue;
				}
				
				// 只对同一类别进行NMS
				if (sorted_boxes[i].classId == sorted_boxes[j].classId) {
					float iou = calculateIoU(sorted_boxes[i], sorted_boxes[j]);
					if (iou > nms_threshold) {
						suppressed[j] = true;
					}
				}
			}
		}
		
		return result;
	}

	// 后处理主函数
	std::vector<DetectBox> Detect_White_Board::postprocess(rknn_output* outputs, 
									int img_h, int img_w,float ratio) {
		
		std::vector<DetectBox> detectResult;
		int gridIndex = -2;
		
		for (int index = 0; index < head_num; ++index) {
			float* reg = (float*)outputs[index * 2].buf;
			float* cls = (float*)outputs[index * 2 + 1].buf;
			
			const int grid_h = map_size[index][0];
			const int grid_w = map_size[index][1];
			const int grid_size = grid_h * grid_w;
			
			for (int h = 0; h < grid_h; ++h) {
				for (int w = 0; w < grid_w; ++w) {
					gridIndex += 2;
					
					// 获取最大类别分数
					float cls_max = 0.0f;
					int cls_index = 0;
					
					if (class_num == 1) {
						cls_max = cls[h * grid_w + w];
						cls_index = 0;
					} else {
						for (int cl = 0; cl < class_num; ++cl) {
							float cls_val = cls[cl * grid_size + h * grid_w + w];
							if (cl == 0) {
								cls_max = cls_val;
								cls_index = cl;
							} else {
								if (cls_val > cls_max) {
									cls_max = cls_val;
									cls_index = cl;
								}
							}
						}
						// cls_max = sigmoid(cls_max);
					}
					
					// 应用置信度阈值
					if (cls_max > conf_thres()) {
						// printf("大于\n");
						// 处理回归值
						std::vector<float> regdfl(4, 0.0f);
						
						for (int lc = 0; lc < 4; ++lc) {
							float sfsum = 0.0f;
							float locval = 0.0f;
							
							// 计算softmax
							for (int df = 0; df < 16; ++df) {
								int idx = ((lc * 16) + df) * grid_size + h * grid_w + w;
								float temp = std::exp(reg[idx]);
								reg[idx] = temp;
								sfsum += temp;
							}
							// 计算位置值
							for (int df = 0; df < 16; ++df) {
								int idx = ((lc * 16) + df) * grid_size + h * grid_w + w;
								float sfval = reg[idx] / sfsum;
								locval += sfval * df;
							}
							
							regdfl[lc] = locval;
						}
						
						// 计算边界框坐标
						float x1 = (meshgrid[gridIndex] - regdfl[0]) * strides[index];
						float y1 = (meshgrid[gridIndex + 1] - regdfl[1]) * strides[index];
						float x2 = (meshgrid[gridIndex] + regdfl[2]) * strides[index];
						float y2 = (meshgrid[gridIndex + 1] + regdfl[3]) * strides[index];
						// printf("x1: %f  y1: %f  x2: %f  y2: %f",x1,y1,x2,y2);

						
						float xmin = x1;
						xmin /= ratio;
						float ymin = y1;
						ymin /= ratio;
						float xmax = x2;
						xmax /= ratio;
						float ymax = y2;
						ymax /= ratio;
						

						// 边界检查
						xmin = std::max(0.0f, xmin);
						ymin = std::max(0.0f, ymin);
						xmax = std::min(static_cast<float>(img_w), xmax);
						ymax = std::min(static_cast<float>(img_h), ymax);
						
						detectResult.emplace_back(cls_index, cls_max, xmin, ymin, xmax, ymax);
					}
				}
			}
		}
		// 先进行TopK筛选
		std::vector<DetectBox> topk_result = TopK(detectResult);
		// 然后应用NMS
		std::vector<DetectBox> nms_result = applyNMS(topk_result, nms_thres());
		// std::cout << "before topK num is: " << detectResult.size() << std::endl;
		return nms_result;
	}

	cv::Mat Detect_White_Board::letter_box(const cv::Mat& im, const cv::Size& target_size, float& ratio,float& dw,float& dh,const cv::Scalar& pad_color) 
	{
		
		const int h = im.rows;
		const int w = im.cols;
		
		ratio = std::min(static_cast<float>(target_size.height) / h, 
						static_cast<float>(target_size.width) / w);
		
		cv::Size new_unpad(static_cast<int>(std::round(w * ratio)), 
						static_cast<int>(std::round(h * ratio)));
		
		dw = target_size.width - new_unpad.width;
		dh = target_size.height - new_unpad.height;

		cv::Mat processed = im.clone();
		
		if (w != new_unpad.width || h != new_unpad.height) {
			cv::resize(processed, processed, new_unpad, 0, 0, cv::INTER_LINEAR);
		} else {
		}
		
		cv::copyMakeBorder(processed, processed, 
						0, dh, 0, dw,
						cv::BORDER_CONSTANT, pad_color);
		
		return processed;
	}

    Status Detect_White_Board::run(stWhiteBoardInfo & info, stWhiteBoardResult & result)
    {
		int person_h = info.person_box.ymax() - info.person_box.ymin();
		
		float hand_center_x = info.hand_box.center_x();
		float hand_center_y = info.hand_box.center_y();
		
		float crop_multiplier = 2.0f;
		
		float crop_xmin = hand_center_x - person_h * crop_multiplier;
		float crop_ymin = hand_center_y - person_h * crop_multiplier;
		float crop_xmax = hand_center_x + person_h * crop_multiplier;
		float crop_ymax = hand_center_y + person_h * crop_multiplier;
		
		crop_xmin = std::max(0.0f, crop_xmin);
		crop_ymin = std::max(0.0f, crop_ymin);
		crop_xmax = std::min(static_cast<float>(info.image.cols - 1), crop_xmax);
		crop_ymax = std::min(static_cast<float>(info.image.rows - 1), crop_ymax);
		
		cv::Rect crop_rect(
			static_cast<int>(crop_xmin), 
			static_cast<int>(crop_ymin), 
			static_cast<int>(crop_xmax - crop_xmin + 1), 
			static_cast<int>(crop_ymax - crop_ymin + 1)
		);
		
		cv::Mat cropped_image = info.image(crop_rect);
		int img_width = cropped_image.cols;
        int img_height = cropped_image.rows;
		float ratio;
        float dw;
        float dh;
		cv::Mat result1 = letter_box(cropped_image, cv::Size(640,640),ratio,dw,dh);

		rknn_input inputs[1];
        memset(inputs, 0, sizeof(inputs));
        inputs[0].index = 0;
        inputs[0].type = RKNN_TENSOR_UINT8;
        inputs[0].size = _inputWidth * _inputHeight * _inputChannel;
        inputs[0].fmt = RKNN_TENSOR_NHWC;
        inputs[0].pass_through = 0;
		inputs[0].buf = result1.data;

		rknn_inputs_set(ctx, io_num.n_input, inputs);

		rknn_output outputs[io_num.n_output];
        memset(outputs, 0, sizeof(outputs));
        for (int i = 0; i < io_num.n_output; i++)
        {
            outputs[i].index = i;
            outputs[i].want_float = 1;
        }

		int ret = rknn_run(ctx, NULL);
        if (ret < 0) {
            LOGE("detect_white_board rknn_run error ret=%d\n", ret);
        }
        
        ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);
        if (ret < 0) {
            LOGE("detect_white_board rknn_outputs_get error: %d\n", ret);
        }

		std::vector<DetectBox> predbox = postprocess(outputs, img_height, img_width, ratio);
		for (auto& box : predbox) 
		{
			box.xmin += crop_xmin;
			box.ymin += crop_ymin;
			box.xmax += crop_xmin;
			box.ymax += crop_ymin;
		}

		if (predbox.size() == 0) {
			result.bfind = false;
		}
		else if (predbox.size() == 1) {
			DetectBox best_box = predbox[0];
			BboxF bboxF(best_box.xmin, best_box.ymin, best_box.xmax, best_box.ymax, best_box.score, best_box.classId);
			result.bfind = true;
			result.white_board_box = bboxF;
		}
		else {
			DetectBox best_box = predbox[0];
			float max_score = best_box.score;
			
			for (size_t i = 1; i < predbox.size(); ++i) {  // 从第二个开始比较
				if (predbox[i].score > max_score) {
					max_score = predbox[i].score;
					best_box = predbox[i];
				}
			}
			BboxF bboxF(best_box.xmin, best_box.ymin, best_box.xmax, best_box.ymax, best_box.score, best_box.classId);
			result.bfind = true;
			result.white_board_box = bboxF;
		}

		ret = rknn_outputs_release(ctx, io_num.n_output, outputs);
		if (ret < 0) {
            LOGE("detect_white_board rknn_outputs_release error ret=%d\n", ret);
        }
        return CZCV_OK;
    }
}